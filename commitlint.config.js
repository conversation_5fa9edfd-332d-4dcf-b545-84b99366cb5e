/** @type {import('@commitlint/types').UserConfig} */
module.exports = {
  extends: ['@commitlint/config-angular'],
  rules: {
    'references-empty': [2, 'never'],
    'header-max-length': [2, 'always', 100],
  },
  parserPreset: {
    parserOpts: {
      issuePrefixes: [
        'GROOM-',
        'OBV-',
        'APP-',
        'CS-',
        'MOE-',
        'TECH-',
        'ERP-',
        'MER-',
        'FDN-',
        'FIN-',
        'IFRFE-',
        'CA-',
        'IQ-',
        'CRM-',
        'MP-',
        'GRM-',
      ],
    },
  },
};
