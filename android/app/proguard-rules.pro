# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in /usr/local/Cellar/android-sdk/24.3.3/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the proguardFiles
# directive in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# react-native-reanimated
-keep class com.swmansion.reanimated.** { *; }
-keep class com.facebook.react.turbomodule.** { *; }
-keep class com.facebook.react.** { *; }
-keep class com.facebook.hermes.** { *; }

# Add any project specific keep options here:

# Square SDK 相关规则
-keep class com.squareup.** { *; }
-keep class com.squareup.sdk.** { *; }
-keep class com.squareup.sdk.reader.** { *; }
-keep class com.squareup.sdk.pos.** { *; }
-dontwarn com.squareup.**

# 防止 Radiography 在生产环境运行
-assumenosideeffects class radiography.** {
    *;
}

# NFC 相关
-keep class android.nfc.** { *; }

# 保持自定义的 ANR 优化类
-keep class com.moement.moego.business.AnrOptimization { *; }
