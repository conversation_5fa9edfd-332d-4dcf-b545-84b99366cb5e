<manifest xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:tools="http://schemas.android.com/tools">
  <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
  <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
  <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
  <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
  <uses-permission android:name="android.permission.BLUETOOTH" />
  <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
  <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
  <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
  <uses-permission android:name="android.permission.BLUETOOTH_SCAN" />
  <uses-permission android:name="android.permission.CAMERA" />
  <uses-permission android:name="android.permission.CAMERA_ROLL" />
  <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
  <uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION" />
  <uses-permission android:name="android.permission.INTERNET" />
  <uses-permission android:name="android.permission.MANAGE_OWN_CALLS" />
  <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
  <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
  <uses-permission android:name="android.permission.READ_CALENDAR" />
  <uses-permission android:name="android.permission.READ_CONTACTS" />
  <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
  <uses-permission android:name="android.permission.READ_PHONE_STATE" />
  <uses-permission android:name="android.permission.RECORD_AUDIO" />
  <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
  <uses-permission android:name="android.permission.USE_BIOMETRIC" />
  <uses-permission android:name="android.permission.USE_FINGERPRINT" />
  <uses-permission android:name="android.permission.USE_FINGERPRINT" />
  <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />
  <uses-permission android:name="android.permission.VIBRATE" />
  <uses-permission android:name="android.permission.WAKE_LOCK" />
  <uses-permission android:name="android.permission.WRITE_CALENDAR" />
  <uses-permission android:name="android.permission.WRITE_CONTACTS" />
  <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
    tools:replace="android:maxSdkVersion" android:maxSdkVersion="34" />
  <uses-permission android:name="android.permission.WRITE_SETTINGS" />
  <uses-permission android:name="android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS" />
  <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
  <uses-feature android:name="android.hardware.camera" android:required="false" />
  <queries>
    <intent>
      <action android:name="android.intent.action.VIEW" />
      <category android:name="android.intent.category.BROWSABLE" />
      <data android:scheme="https" />
    </intent>
  </queries>
  <application android:name=".MainApplication" android:label="@string/app_name"
    android:icon="@mipmap/ic_launcher" android:roundIcon="@mipmap/ic_launcher_round"
    android:allowBackup="true" android:theme="@style/AppTheme"
    android:requestLegacyExternalStorage="true">
    <meta-data android:name="com.google.android.geo.API_KEY"
      android:value="AIzaSyAmUHw_EPnvcw2N0NM574N1sHazZMcP4Fg" />
    <meta-data android:name="com.google.firebase.messaging.default_notification_color"
      android:resource="@color/notification_icon_color" />
    <meta-data android:name="com.google.firebase.messaging.default_notification_icon"
      android:resource="@drawable/notification_icon" />
    <meta-data android:name="expo.modules.notifications.default_notification_color"
      android:resource="@color/notification_icon_color" />
    <meta-data android:name="expo.modules.notifications.default_notification_icon"
      android:resource="@drawable/notification_icon" />
    <meta-data android:name="expo.modules.updates.ENABLED" android:value="true" />
    <meta-data android:name="expo.modules.updates.EXPO_RUNTIME_VERSION"
      android:value="@string/expo_runtime_version" />
    <meta-data android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
      android:value="ALWAYS" />
    <meta-data android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS" android:value="0" />
    <meta-data android:name="expo.modules.updates.EXPO_UPDATE_URL"
      android:value="@string/expo_updates_url" />
    <meta-data android:name="expo.modules.updates.UPDATES_CONFIGURATION_REQUEST_HEADERS_KEY"
      android:value="{&quot;moego-app-name&quot;:&quot;moego-business&quot;}" />
    <meta-data android:name="io.intercom.android.sdk.server.region"
      android:value="@integer/intercom_server_region_us" />
    <!-- square sdk 声明前台服务 -->
    <service
      android:name="com.squareup.queue.QueueService"
      android:exported="false"
      android:foregroundServiceType="mediaPlayback" />

    <activity android:name=".MainActivity"
      android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode|locale|layoutDirection"
      android:launchMode="singleTask" android:windowSoftInputMode="adjustResize"
      android:theme="@style/Theme.App.SplashScreen" android:exported="true"
      android:screenOrientation="portrait">
      <intent-filter>
        <action android:name="android.intent.action.MAIN" />
        <category android:name="android.intent.category.LAUNCHER" />
      </intent-filter>
      <intent-filter>
        <action android:name="android.intent.action.VIEW" />
        <category android:name="android.intent.category.DEFAULT" />
        <category android:name="android.intent.category.BROWSABLE" />
        <data android:scheme="com.moement.moego.business" />
        <data android:scheme="exp+moego-business-2" />
      </intent-filter>
    </activity>
    <activity android:name="com.facebook.react.devsupport.DevSettingsActivity"
      android:exported="false" />
    <uses-library android:name="org.apache.http.legacy" android:required="false" />
  </application>
</manifest>
