export enum QuestionType {
  ShortText = 1,
  LongText = 2,
  Dropdown = 3,
  Radio = 4,
  Checkbox = 5,
}

export enum PetQuestionKey {
  PetImage = 'Pet_image',
  Breed = 'Pet_breed',
  PetType = 'Pet_type',
  PetName = 'Pet_name',
  Fixed = 'Fixed',
  Birthday = 'Birthday',
  Gender = 'Gender',
  VaccineList = 'Vaccine',
  VaccineDocument = 'Vaccine_document',
  Weight = 'Weight',
  VetName = 'Vet_name',
  VetPhone = 'Vet_phone_number',
  VetAddress = 'Vet_address',
  HealthIssues = 'Health_issues',
  Behavior = 'Behavior',
  HairLength = 'Hair_length',
  CoatType = 'Coat_type',
}

export enum CustomerQuestionKey {
  FirstName = 'First_name',
  LastName = 'Last_name',
  PhoneNumber = 'Phone_number',
  Email = 'Email',
  Address = 'Address',
  AdditionalNode = 'Additional_note',
  Agreement = 'Agreement',
  ReferralSource = 'Referral_source',
  PreferredGroomer = 'Preferred_groomer',
  PreferredFrequency = 'Preferred_frequency',
  PreferredDayOfTheWeek = 'Preferred_day_of_the_week',
  PreferredTimeOfTheDay = 'Preferred_time_of_the_day',
  Birthday = 'Birthday',
  EmergencyContact = 'Emergency_contact',
  PeopleAuthorizeToPickupPets = 'People_authorized_to_pickup_pets',
}

export enum MetaQuestionKey {
  Medication_schedule = 'Medication_schedule',
  Feeding_schedule = 'Feeding_schedule',
}

export enum MetaMedicationKeyEnum {
  medicationTime = 'medicationTime',
  medicationAmount = 'medicationAmount',
  medicationUnit = 'medicationUnit',
  medicationName = 'medicationName',
  medicationNotes = 'medicationNotes',
  selectedDate = 'selectedDate',
}

export enum MetaMedicationSelectedDateEnum {
  dateType = 'selectedDate.dateType',
  specificDates = 'selectedDate.specificDates',
}

export enum MetaFeedingKeyEnum {
  feedingTime = 'feedingTime',
  feedingAmount = 'feedingAmount',
  feedingUnit = 'feedingUnit',
  foodType = 'foodType',
  foodSource = 'foodSource',
  feedingInstruction = 'feedingInstruction',
  feedingNote = 'feedingNote',
}

export enum MetaFeedingMedicationKey {
  FeedingKey = 'feeding',
  MedicationKey = 'medication',
}
