/*
 * @since 2023-04-12 10:11:29
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.api.v3.metadata.controller;

import static com.moego.idl.models.errors.v1.Code.CODE_UNAUTHORIZED_ERROR;
import static com.moego.idl.models.metadata.v1.OwnerType.OWNER_TYPE_ACCOUNT;
import static com.moego.idl.models.metadata.v1.OwnerType.OWNER_TYPE_ACCOUNT_VALUE;
import static com.moego.idl.models.metadata.v1.OwnerType.OWNER_TYPE_BUSINESS;
import static com.moego.idl.models.metadata.v1.OwnerType.OWNER_TYPE_BUSINESS_VALUE;
import static com.moego.idl.models.metadata.v1.OwnerType.OWNER_TYPE_COMPANY;
import static com.moego.idl.models.metadata.v1.OwnerType.OWNER_TYPE_COMPANY_VALUE;
import static com.moego.idl.models.metadata.v1.OwnerType.OWNER_TYPE_STAFF;
import static com.moego.idl.models.metadata.v1.OwnerType.OWNER_TYPE_STAFF_VALUE;
import static com.moego.idl.models.metadata.v1.OwnerType.OWNER_TYPE_SYSTEM;
import static com.moego.idl.models.metadata.v1.PermissionLevel.PERMISSION_LEVEL_BUSINESS_ANY_STAFF;
import static com.moego.idl.models.metadata.v1.PermissionLevel.PERMISSION_LEVEL_BUSINESS_ANY_STAFF_VALUE;
import static com.moego.idl.models.metadata.v1.PermissionLevel.PERMISSION_LEVEL_NOBODY;
import static com.moego.idl.models.metadata.v1.PermissionLevel.PERMISSION_LEVEL_OWNER;
import static com.moego.idl.models.metadata.v1.PermissionLevel.PERMISSION_LEVEL_OWNER_VALUE;
import static com.moego.lib.common.auth.AuthContext.HK_ACCOUNT_ID;
import static com.moego.lib.common.auth.AuthContext.HK_BUSINESS_ID;
import static com.moego.lib.common.auth.AuthContext.HK_COMPANY_ID;
import static com.moego.lib.common.auth.AuthContext.HK_STAFF_ID;
import static com.moego.lib.common.exception.ExceptionUtil.extractCommonError;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrowsExactly;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.moego.api.v3.metadata.converter.MetadataConverter;
import com.moego.api.v3.utils.MockStubs;
import com.moego.api.v3.utils.TestUtils;
import com.moego.common.enums.StaffEnum;
import com.moego.idl.api.metadata.v1.MetadataApiServiceGrpc.MetadataApiServiceBlockingStub;
import com.moego.idl.models.metadata.v1.KeyModel;
import com.moego.idl.models.metadata.v1.OwnerType;
import com.moego.idl.models.metadata.v1.PermissionLevel;
import com.moego.idl.service.metadata.v1.ExtractValuesRequest;
import com.moego.idl.service.metadata.v1.GetKeyRequest;
import com.moego.idl.service.metadata.v1.GetKeyResponse;
import com.moego.idl.service.metadata.v1.UpdateValueRequest;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.business.dto.CompanyDto;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.params.StaffIdParams;

import io.grpc.ClientInterceptor;
import io.grpc.Metadata;
import io.grpc.StatusRuntimeException;
import io.grpc.stub.MetadataUtils;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@ExtendWith(MockitoExtension.class)
@SpringBootTest
public class MetadataApiControllerTest extends MockStubs {

    @Autowired
    private MetadataApiServiceBlockingStub metadataApiServiceBlockingStub;

    @Autowired
    private MetadataConverter metadataConvertor;

    @BeforeEach
    void beforeEach() {
        when(iBusinessBusinessClient.getCompanyByBusinessId(any()))
                .thenReturn(CompanyDto.builder().id(1).build());
    }



    private ClientInterceptor withHeaders(Map<String, String> headers) {
        var metadata = new Metadata();
        for (Map.Entry<String, String> item : headers.entrySet()) {
            var key = Metadata.Key.of(item.getKey(), Metadata.ASCII_STRING_MARSHALLER);
            metadata.put(key, item.getValue());
        }
        return MetadataUtils.newAttachHeadersInterceptor(metadata);
    }

    @Test
    public void extractValues() {
        when(metadataServiceClient.extractValues(any())).thenAnswer(invocation -> {
            var request = invocation.<ExtractValuesRequest>getArgument(0);
            return metadataConvertor.toExtractValuesResponse(
                    0,
                    Map.of(
                            "group",
                            request.hasGroup() ? request.getGroup() : "NULL",
                            "keyName",
                            request.hasKeyName() ? request.getKeyName() : "NULL",
                            "keyId",
                            request.hasKeyId() ? String.valueOf(request.getKeyId()) : "NULL",
                            "owners",
                            JsonUtil.toJson(request.getOwnersMap().entrySet().stream()
                                    .sorted(Comparator.comparingLong(Map.Entry::getKey))
                                    .map(v -> List.of(v.getKey(), v.getValue()))
                                    .toList())));
        });

        var output =
                metadataApiServiceBlockingStub.describeMetadata(metadataConvertor.toDescribeMetadataParams("A", null));
        assertEquals(
                metadataConvertor.toDescribeMetadataResult(
                        0, Map.of("group", "A", "keyName", "NULL", "keyId", "NULL", "owners", "[]")),
                output);

        output =
                metadataApiServiceBlockingStub.describeMetadata(metadataConvertor.toDescribeMetadataParams(null, "KK"));
        assertEquals(
                metadataConvertor.toDescribeMetadataResult(
                        0, Map.of("group", "NULL", "keyName", "KK", "keyId", "NULL", "owners", "[]")),
                output);

        output = metadataApiServiceBlockingStub
                .withInterceptors(withHeaders(Map.of(HK_ACCOUNT_ID, "3", HK_STAFF_ID, "2", HK_BUSINESS_ID, "1")))
                .describeMetadata(metadataConvertor.toDescribeMetadataParams("G", null));
        assertEquals(
                metadataConvertor.toDescribeMetadataResult(
                        0,
                        Map.of(
                                "group",
                                "G",
                                "keyName",
                                "NULL",
                                "keyId",
                                "NULL",
                                "owners",
                                JsonUtil.toJson(new long[][] {
                                    {OWNER_TYPE_COMPANY_VALUE, 1},
                                    {OWNER_TYPE_BUSINESS_VALUE, 1},
                                    {OWNER_TYPE_STAFF_VALUE, 2},
                                    {OWNER_TYPE_ACCOUNT_VALUE, 3},
                                }))),
                output);
    }

    @Test
    public void updateValue() {
        when(metadataServiceClient.getKey(any())).thenAnswer(invocation -> {
            var input = invocation.<GetKeyRequest>getArgument(0);
            var ownerType = OwnerType.forNumber(Integer.parseInt(input.getName().split(",")[0]));
            var permissionLevel =
                    PermissionLevel.forNumber(Integer.parseInt(input.getName().split(",")[1]));
            var id = ownerType.getNumber() + permissionLevel.getNumber() * 10;
            return GetKeyResponse.newBuilder()
                    .setKey(KeyModel.newBuilder()
                            .setId(id)
                            .setName(input.getName())
                            .setOwnerType(ownerType)
                            .setPermissionLevel(permissionLevel)
                            .build())
                    .build();
        });

        var updateValueRequest = new AtomicReference<UpdateValueRequest>();

        when(metadataServiceClient.updateValue(any())).thenAnswer(invocation -> {
            updateValueRequest.set(invocation.getArgument(0));
            return null;
        });
        when(iBusinessStaffClient.getStaff(any())).thenAnswer(invocation -> {
            var params = invocation.<StaffIdParams>getArgument(0);
            var result = new MoeStaffDto();
            result.setId(params.getStaffId());
            result.setBusinessId(params.getBusinessId());
            result.setEmployeeCategory(
                    params.getStaffId().equals(OWNER_TYPE_STAFF_VALUE)
                            ? StaffEnum.EMPLOYEE_CATEGORY_OWNER
                            : StaffEnum.EMPLOYEE_CATEGORY_STAFF);
            return result;
        });
        when(iBusinessStaffClient.getOwnerStaffId(any())).thenAnswer(invocation -> {
            var businessId = invocation.<Integer>getArgument(0);
            return businessId.equals(OWNER_TYPE_BUSINESS_VALUE) ? OWNER_TYPE_STAFF_VALUE : null;
        });

        // system
        var exception = assertThrowsExactly(
                StatusRuntimeException.class,
                () -> updateMetadata(
                        OWNER_TYPE_ACCOUNT_VALUE,
                        OWNER_TYPE_BUSINESS_VALUE,
                        OWNER_TYPE_STAFF_VALUE,
                        OWNER_TYPE_SYSTEM,
                        PERMISSION_LEVEL_OWNER,
                        null));
        assertEquals(extractCommonError(exception).getCode(), CODE_UNAUTHORIZED_ERROR);

        // company
        assertThrowsExactly(
                StatusRuntimeException.class,
                () -> updateMetadata(
                        OWNER_TYPE_ACCOUNT_VALUE,
                        OWNER_TYPE_BUSINESS_VALUE,
                        OWNER_TYPE_STAFF_VALUE,
                        OWNER_TYPE_COMPANY,
                        PERMISSION_LEVEL_OWNER,
                        null));
        assertEquals(extractCommonError(exception).getCode(), CODE_UNAUTHORIZED_ERROR);

        // business owner OK
        updateMetadata(
                OWNER_TYPE_ACCOUNT_VALUE,
                OWNER_TYPE_BUSINESS_VALUE,
                OWNER_TYPE_STAFF_VALUE,
                OWNER_TYPE_BUSINESS,
                PERMISSION_LEVEL_OWNER,
                null);
        assertEquals(
                UpdateValueRequest.newBuilder()
                        .setKeyId(OWNER_TYPE_BUSINESS_VALUE + PERMISSION_LEVEL_OWNER_VALUE * 10)
                        .setOwnerId(OWNER_TYPE_BUSINESS_VALUE)
                        .setOperatorId(OWNER_TYPE_ACCOUNT_VALUE)
                        .build(),
                updateValueRequest.get());

        // business owner FAIL
        assertThrowsExactly(
                StatusRuntimeException.class,
                () -> updateMetadata(
                        OWNER_TYPE_ACCOUNT_VALUE,
                        OWNER_TYPE_BUSINESS_VALUE,
                        OWNER_TYPE_ACCOUNT_VALUE,
                        OWNER_TYPE_BUSINESS,
                        PERMISSION_LEVEL_OWNER,
                        null));
        assertEquals(extractCommonError(exception).getCode(), CODE_UNAUTHORIZED_ERROR);

        // business nobody
        assertThrowsExactly(
                StatusRuntimeException.class,
                () -> updateMetadata(
                        OWNER_TYPE_ACCOUNT_VALUE,
                        OWNER_TYPE_BUSINESS_VALUE,
                        OWNER_TYPE_STAFF_VALUE,
                        OWNER_TYPE_BUSINESS,
                        PERMISSION_LEVEL_NOBODY,
                        null));
        assertEquals(extractCommonError(exception).getCode(), CODE_UNAUTHORIZED_ERROR);

        // business staff
        // should never throw for business id & staff id is set
        updateMetadata(
                OWNER_TYPE_ACCOUNT_VALUE,
                OWNER_TYPE_BUSINESS_VALUE,
                OWNER_TYPE_ACCOUNT_VALUE,
                OWNER_TYPE_BUSINESS,
                PERMISSION_LEVEL_BUSINESS_ANY_STAFF,
                "NULL");
        assertEquals(
                UpdateValueRequest.newBuilder()
                        .setKeyId(OWNER_TYPE_BUSINESS_VALUE + PERMISSION_LEVEL_BUSINESS_ANY_STAFF_VALUE * 10)
                        .setOwnerId(OWNER_TYPE_BUSINESS_VALUE)
                        .setValue("NULL")
                        .setOperatorId(OWNER_TYPE_ACCOUNT_VALUE)
                        .build(),
                updateValueRequest.get());

        // staff nobody
        assertThrowsExactly(
                StatusRuntimeException.class,
                () -> updateMetadata(
                        OWNER_TYPE_ACCOUNT_VALUE,
                        OWNER_TYPE_BUSINESS_VALUE,
                        OWNER_TYPE_STAFF_VALUE,
                        OWNER_TYPE_STAFF,
                        PERMISSION_LEVEL_NOBODY,
                        null));
        assertEquals(extractCommonError(exception).getCode(), CODE_UNAUTHORIZED_ERROR);

        // staff anonymous
        assertThrowsExactly(
                StatusRuntimeException.class,
                () -> updateMetadata(
                        OWNER_TYPE_ACCOUNT_VALUE, null, null, OWNER_TYPE_STAFF, PERMISSION_LEVEL_OWNER, null));
        assertEquals(extractCommonError(exception).getCode(), CODE_UNAUTHORIZED_ERROR);

        // staff OK
        updateMetadata(
                OWNER_TYPE_ACCOUNT_VALUE,
                OWNER_TYPE_BUSINESS_VALUE,
                OWNER_TYPE_STAFF_VALUE,
                OWNER_TYPE_STAFF,
                PERMISSION_LEVEL_OWNER,
                null);
        assertEquals(
                UpdateValueRequest.newBuilder()
                        .setKeyId(OWNER_TYPE_STAFF_VALUE + PERMISSION_LEVEL_OWNER_VALUE * 10)
                        .setOwnerId(OWNER_TYPE_STAFF_VALUE)
                        .setOperatorId(OWNER_TYPE_ACCOUNT_VALUE)
                        .build(),
                updateValueRequest.get());

        // account nobody
        assertThrowsExactly(
                StatusRuntimeException.class,
                () -> updateMetadata(
                        OWNER_TYPE_ACCOUNT_VALUE,
                        OWNER_TYPE_BUSINESS_VALUE,
                        OWNER_TYPE_STAFF_VALUE,
                        OWNER_TYPE_ACCOUNT,
                        PERMISSION_LEVEL_NOBODY,
                        null));
        assertEquals(extractCommonError(exception).getCode(), CODE_UNAUTHORIZED_ERROR);

        // account anonymous
        assertThrowsExactly(
                StatusRuntimeException.class,
                () -> updateMetadata(null, null, null, OWNER_TYPE_ACCOUNT, PERMISSION_LEVEL_OWNER, null));
        assertEquals(extractCommonError(exception).getCode(), CODE_UNAUTHORIZED_ERROR);

        // account OK
        updateMetadata(OWNER_TYPE_ACCOUNT_VALUE, null, null, OWNER_TYPE_ACCOUNT, PERMISSION_LEVEL_OWNER, null);
        assertEquals(
                UpdateValueRequest.newBuilder()
                        .setKeyId(OWNER_TYPE_ACCOUNT_VALUE + PERMISSION_LEVEL_OWNER_VALUE * 10)
                        .setOwnerId(OWNER_TYPE_ACCOUNT_VALUE)
                        .setOperatorId(OWNER_TYPE_ACCOUNT_VALUE)
                        .build(),
                updateValueRequest.get());
    }

    private void updateMetadata(
            Integer accountId,
            Integer businessId,
            Integer staffId,
            OwnerType ownerType,
            PermissionLevel permissionLevel,
            String value) {
        updateMetadata(accountId, businessId, staffId, OWNER_TYPE_COMPANY_VALUE, ownerType, permissionLevel, value);
    }

    private void updateMetadata(
            Integer accountId,
            Integer businessId,
            Integer staffId,
            Integer companyId,
            OwnerType ownerType,
            PermissionLevel permissionLevel,
            String value) {
        TestUtils.AU(metadataApiServiceBlockingStub)
                .updateMetadata(metadataConvertor.toUpdateMetadataParams(
                        ownerType.getNumber() + "," + permissionLevel.getNumber(), value));
    }
}
