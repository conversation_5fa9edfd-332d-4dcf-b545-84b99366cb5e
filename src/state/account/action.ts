import {
  type OBCheckIdentifierRequest,
  type OBSendVerificationCodeRequest,
} from '@moego/api-web/moego/api/online_booking/v1/ob_access_api';
import { type UpdateOBClientRequest } from '@moego/api-web/moego/api/online_booking/v1/ob_client_api';
import { type CreateCustomerParams } from '@moego/api-web/moego/client/online_booking/v1/business_customer_api';
import { AccessType } from '@moego/api-web/moego/models/online_booking/v1/ob_access_enums';
import { type z } from '@moego/bff-openapi';
import {
  Customer_Type,
  type CreateCustomerWithPetRequestForOB,
  type schemas,
} from '@moego/bff-openapi/clients/client.leads';
import { growthBook } from '@moego/client-lib-growthbook/dist/setup';
import { type DeepPartial } from '@moego/client-lib-http/dist/http-client';
import { actionAtom } from '@moego/client-lib-jotai/dist/action';
import { atom } from 'jotai';
import { http } from 'libs/api';
import { BffLeadsClient } from 'libs/bff';
import { BusinessCustomerServiceClient, OBAccessClient, OBAddressClient, OBClientClient } from 'libs/clients';
import { getBookOnlineCustomerAdditionalParams } from 'pages/PersonalInfo/hooks/useSubmitBookingParams';
import { transformBirthday } from 'pages/PersonalInfo/hooks/useSubmitCustomerParams';
import { toast } from 'react-hot-toast';
import { adsState } from 'state/ads/state';
import { bookingPhoneState, type BookingPhoneState } from 'state/booking/bookingPhoneState';
import { newCustomerInfoState, type CustomerInfoValue } from 'state/customer/state';
import { type CustomerAddressEntity, type CustomerEntity } from 'types/entity';
import { CustomerQuestionKey } from 'types/question';
import { flattenCustomer } from 'utils/customer';
import { GrowthBookFeatureList } from 'utils/growthBook';
import { omitMap } from 'utils/map';
import { internalCustomerFormKeys } from 'utils/question';
import { sendVerificationCodeAction, type SendVerificationCodeRes } from '../../pages/Book/action';
import { promiseWrapper } from '../../utils/promise';
import { bookingKeyState, bookingNamePayload, businessInfoState, companyIdState } from '../business/state';
import {
  accountValidateState,
  customerAddressesState,
  customerIdState,
  customerInfoState,
  didLoginState,
  type CreateLeadRequest,
  type CustomerAddressState,
  type UpdateLeadRequest,
} from './state';
import { transformCustomerAddress2UpdateInfoAddress } from './utils';

export const getNewPhoneState = (
  newVal: string,
  additionalContact?:
    | {
        phoneNumber: string;
        relatedClientId: string;
      }
    | undefined,
): BookingPhoneState => {
  return {
    phoneNumber: newVal,
    time: Date.now(),
    additionalContact,
  };
};

export const getCustomerInfoAction = actionAtom(async (get, set, force = true) => {
  if (force) {
    set(customerInfoState, { loading: true, data: undefined });
  }
  const payload = get(bookingNamePayload);
  const res = await OBClientClient.getOBClientInfo({}, { query: payload });
  const result = flattenCustomer(res);
  // 这里的phoneNumber可能是maskedPhoneNumber，所以我们登录完之后需要更新bookingPhoneState
  result.phoneNumber && set(bookingPhoneState, (pre) => ({ ...pre, ...getNewPhoneState(result.phoneNumber!) }));
  set(customerInfoState, { loading: false, data: result });

  set(newCustomerInfoState, (pre) => ({
    ...pre,
    additional: {
      ...pre?.additional,
      [CustomerQuestionKey.Birthday]: result.birthday?.replace(/(Z|z)$/, ''), // 后端返回来的数据时区未解析，需要去掉，否则前端获取值时校验过不去
    },
  }));
});

export const loginByVerificationAction = actionAtom(
  async (get, set, { verifyCode = '000000' }: { verifyCode?: string }) => {
    const { phoneNumber, additionalContact } = get(bookingPhoneState);
    const { googleAdsParams, utmParams, metaAdsParams } = get(adsState);
    const adsData = {
      ...googleAdsParams,
      ...utmParams,
      ...metaAdsParams,
    };
    const key = get(getWithPhoneKey)(phoneNumber);
    const verificationToken = get(accountValidateState)[key].token;
    const extraParams = additionalContact
      ? {
          additionalContact,
        }
      : { phoneNumber };
    const existAdsOrGAData = Object.keys(adsData).length > 0;
    await OBAccessClient.login(
      {
        byVerificationCode: {
          code: verifyCode,
          accessType: AccessType.BY_PHONE,
          token: verificationToken!,
          ...extraParams,
        },
        ...(existAdsOrGAData ? { adsData: { googleAdsStr: JSON.stringify(adsData) } } : {}),
      },
      { stringifyError: false, skipErrorHandler: true },
    );
  },
);

export const loginByTokenAction = actionAtom(async (get, set, token: string) => {
  await OBAccessClient.login({
    byPpp: { token },
  });
});

export const logoutAction = actionAtom(async () => {
  await OBAccessClient.logout({});
});

const phoneExistsCache = new Map<string, boolean>();

export const checkCustomerExistsAction = atom((get) => {
  const bookingKey = get(bookingKeyState);
  const checkIdentifier = get(checkIdentifierAction);
  const enableLeadManagementV1 = growthBook.isOn(GrowthBookFeatureList.EnableLeadManagementV1);

  return async (params: Omit<OBCheckIdentifierRequest, 'includePossibleClients'>) => {
    const key = `${bookingKey}-${JSON.stringify(params)}`;
    // 只要开启了lead白名单，isExist 就可能短时间内改变，需要重新调用接口
    if (!enableLeadManagementV1 && phoneExistsCache.has(key)) {
      return phoneExistsCache.get(key);
    }
    const { exist } = await checkIdentifier({ ...params, includePossibleClients: false });
    phoneExistsCache.set(key, exist);
    return exist;
  };
});

export const checkIdentifierAction = atom((get) => {
  const payload = get(bookingNamePayload);
  return async (params: OBCheckIdentifierRequest) => {
    return await OBAccessClient.checkIdentifier({ ...payload, ...params });
  };
});

export const createCustomerAction = atom((get) => {
  const payload = get(bookingNamePayload);
  return async (phoneNumber: string, clientInfo?: CustomerInfoValue) => {
    const answersMap = clientInfo?.additional ? omitMap(clientInfo?.additional, internalCustomerFormKeys) : {};
    const additionalInfo = getBookOnlineCustomerAdditionalParams(clientInfo?.additional) as NonNullable<
      CreateCustomerParams['customer']
    >['additionalInfo'];
    const param = {
      ...payload,
      customer: {
        phoneNumber,
        firstName: clientInfo?.basic?.firstName || '',
        lastName: clientInfo?.basic?.lastName || '',
        email: clientInfo?.basic?.email || '',
        ...(clientInfo?.additional?.[CustomerQuestionKey.Birthday]
          ? { birthday: transformBirthday(clientInfo?.additional?.[CustomerQuestionKey.Birthday] as string, true) }
          : {}),
        answersMap,
        address: clientInfo?.basic?.address ?? undefined,
        additionalInfo,
      },
    };
    await BusinessCustomerServiceClient.createCustomer(param);
  };
});

export const getVerificationSettingAction = atom((get) => {
  const payload = get(bookingNamePayload);
  return async () => {
    return await OBAccessClient.getVerificationSetting(payload);
  };
});

export const fetchCustomerAddressesAction = actionAtom(async (get, set) => {
  const didLogin = get(didLoginState);
  const initialData: CustomerAddressState = { inArea: [], outArea: [], all: [] };
  if (!didLogin) {
    set(customerAddressesState, { loading: false, data: initialData });
    return;
  }
  set(customerAddressesState, { loading: true, data: initialData });
  const addresses = await http.open('GET/customer/ob/v2/client/address');
  // no auth || ob disabled
  if (!addresses?.length) {
    set(customerAddressesState, { loading: false, data: initialData });
    return;
  }
  const result = addresses.reduce(
    (accu, address) => {
      const { inArea, outArea } = accu;
      (address.outOfArea ? outArea : inArea).push(address);
      return accu;
    },
    {
      inArea: [],
      outArea: [],
      all: [],
      hasAddress: !!addresses.length,
      hasValidAddress: undefined,
    } as CustomerAddressState,
  );
  result.all = [...result.inArea, ...result.outArea];
  result.hasValidAddress = result.inArea.length > 0;
  set(customerAddressesState, { loading: false, data: result });
});

export const getWithPhoneKey = atom((get) => {
  return (phoneNumber: string) => {
    return `${get(bookingKeyState)}-${phoneNumber}`;
  };
});

export const setAccountValidateStateAction = actionAtom(
  (get, set, value: Omit<BookingPhoneState, 'time'> & { token?: string }) => {
    const key = get(getWithPhoneKey)(value.phoneNumber);
    set(accountValidateState, (i) => {
      const oldValue = i[key];
      const token = value.token || oldValue?.token;
      const additionalContact = value.additionalContact || oldValue?.additionalContact;
      return {
        ...i,
        [key]: { token, additionalContact },
      };
    });
  },
);

export interface SendValidateCodeParams extends Omit<OBSendVerificationCodeRequest, 'accessType'> {
  toast?: boolean;
  onError: (e: string) => void;
  onSuccess?: (res: SendVerificationCodeRes) => void;
}

export const sendValidateCodeAction = actionAtom(async (get, set, params: SendValidateCodeParams) => {
  const { phoneNumber, additionalContact, toast: withToast, onError, onSuccess } = params;

  const sendVerificationCode = get(sendVerificationCodeAction);

  if (withToast) {
    toast.loading('Sending verification code...');
  }

  const extraParams = additionalContact ? { additionalContact } : { phoneNumber };

  const [error, res] = await promiseWrapper(
    sendVerificationCode({
      accessType: AccessType.BY_PHONE,
      ...extraParams,
    }),
  );
  if (error || !res) {
    const res = error as any;
    let message = res.data?.message;
    try {
      // 优先使用 details message，使用 data message 兜底
      message = res.data.details[0].message;
    } finally {
      onError(message || 'Send failed.');
    }
    return;
  }
  set(setAccountValidateStateAction, { phoneNumber: phoneNumber!, additionalContact, token: res.token });
  onSuccess?.(res);
  if (withToast) {
    toast.dismiss();
    toast.success('Verification code sent');
  }
});

export const syncCustomerInfoAction = actionAtom(async (get, set, input: DeepPartial<UpdateOBClientRequest>) => {
  const payload = get(bookingNamePayload);
  const inputData = input as UpdateOBClientRequest;
  const emergencyContact = inputData?.customQuestions?.[CustomerQuestionKey.EmergencyContact];
  if (emergencyContact) {
    delete inputData.customQuestions[CustomerQuestionKey.EmergencyContact];
    Object.assign(inputData, {
      emergencyContact,
    });
  }
  await OBClientClient.updateOBClient(inputData, {
    stringifyError: false,
    skipErrorHandler: true,
    query: payload,
  });
});

export const syncPrimaryAddressAction = actionAtom(async (get, set, input: CustomerAddressEntity) => {
  // 按需给后端
  const transformedInput = transformCustomerAddress2UpdateInfoAddress(input);
  await OBAddressClient.upsertAddress(transformedInput, {
    stringifyError: false,
    skipErrorHandler: true,
  });
});

type CustomerProfileKey = 'firstName' | 'lastName' | 'email' | 'phoneNumber';

export const updateCustomerProfileAction = actionAtom(
  async (get, set, input: DeepPartial<Pick<CustomerEntity, CustomerProfileKey>>) => {
    set(customerInfoState, (prev) => ({
      ...prev,
      data: { ...prev.data, ...input },
    }));
  },
);

export const createLeadAction = actionAtom(async (get, set, input: CreateLeadRequest) => {
  const businessInfo = get(businessInfoState).data;
  const businessId = businessInfo?.setting?.businessId;
  const companyId = get(companyIdState);
  const { customer } = input;

  const { phoneNumber = '', additionalInfo, givenName, familyName, ...others } = customer || {};
  const phoneNumberWithoutSpace = phoneNumber.replace(/\s+/g, '');
  const reqData: CreateCustomerWithPetRequestForOB = {
    customer: {
      ...others,
      phoneNumber: phoneNumberWithoutSpace,
      preferredBusinessId: String(businessId),
      type: Customer_Type.LEAD,
      avatarPath: '',
      source: 'ob',
      givenName: givenName ?? '*' + phoneNumberWithoutSpace.slice(-3),
      familyName: familyName ?? 'lead',
      email: '',
      additionalInfo,
    },
    pets: [],
    companyId: String(companyId),
    businessId: String(businessId),
  };

  await BffLeadsClient.createCustomerForOB(reqData);
});

export const updateLeadAction = actionAtom(async (get, set, input: UpdateLeadRequest) => {
  const businessInfo = get(businessInfoState).data;
  const businessId = businessInfo?.setting?.businessId;
  const companyId = get(companyIdState);
  const customerId = get(customerIdState);

  const { additionalInfo, givenName, familyName, ...others } = input || {};
  const reqData: z.infer<typeof schemas.UpdateCustomerRequestSchemaForOB> = {
    ...others,
    id: String(customerId || ''),
    givenName: givenName ?? '',
    familyName: familyName ?? '',
    additionalInfo,
    companyId: String(companyId),
    businessId: String(businessId),
  };

  await BffLeadsClient.updateCustomerForOB(reqData);
});
