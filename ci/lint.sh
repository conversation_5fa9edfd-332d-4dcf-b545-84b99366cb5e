#!/usr/bin/env bash
# @since 2022-08-04 10:47:10
# <AUTHOR> <EMAIL>

export XDG_DATA_HOME="$PWD/.metro-cache/"
set -euo pipefail

if [ ! -d "$XDG_DATA_HOME" ]; then
  mkdir -p "$XDG_DATA_HOME"
fi

rm -f "$XDG_DATA_HOME/lint.error"

function echo_error() {
  echo -e "\n"
  echo -e "\033[31mERROR: $1\033[0m"
  touch "$XDG_DATA_HOME/lint.error"
}

function echo_info() {
  echo -e "\033[32mINFO: $1\033[0m"
}

function check_native_modules() {
  echo_info 'Checking native modules'
  if grep -E $(echo $(grep -E "from '[^.']+'" ./src/utils/NativeModules.ts -o | awk -F "'" '{print $2}') | sed 's/ /|/g') -r ./src | grep -v src/utils/NativeModules.ts | grep -v .d.ts | grep -v src/utils/const.ts | grep -v src/setupTests.ts:; then
    echo_error 'please import native modules from src/utils/NativeModules.ts rather than node modules directly.'
  fi
}

function check_moment() {
  echo_info 'Checking moment'
  if grep -E 'from .moment.' -r src; then
    echo_error 'moment is deprecated, use dayjs instead.'
  fi
}

function check_ts_ignore() {
  echo_info "Checking ts-ignore"
  if git --no-pager grep -n -i @ts''-ignore; then
    echo_error '@ts''-ignore is forbidden, please fix it or use @ts-expect-error instead.'
  fi
}

function check_generate() {
  echo_info 'Checking generated files'
  if git status -s | grep src/entry/generated/; then
    echo_error 'The generated directory is dirty, please stash or commit it first.'
  fi
  npm run generate
  if git status -s | grep src/entry/generated/; then
    echo_error 'Please do not edit /src/entry/generated/ directory, use \`npm run generate\` to generate it.'
  fi
}

function check_index() {
  echo_info "Checking index files"
  if git ls-files src/ | grep /index; then
    echo_error 'index.* is forbidden, please use valid file name instead.'
  fi
}

function check_default() {
  echo_info "Checking export default"
  if git grep 'export default' -- src | grep -v '.d.ts:' | grep -v 'src/modules/'; then
    echo_error 'export default is forbidden, please use export { xxx } instead.'
  fi
}

function check_circular() {
  echo_info "Checking circular dependencies"
  if ! npx dpdm -T --no-tree --no-warning --exit-code circular:1 index.js; then
    echo_error 'Circular dependencies detected, please fix it.'
  fi
}

function check_prettier() {
  echo_info "Checking prettier"
  if ! npx prettier --check --cache --cache-strategy metadata .; then
    echo_error 'Code is not formatted correctly, please fix it.'
  fi
}

function check_types() {
  echo_info "Checking types"
  if ! npx tsc --noEmit; then
    echo_error 'Ts check failed, please fix it.'
  fi
}

function check_all() {
  check_circular &
  # check_prettier &
  check_types &
  check_ts_ignore &
  check_index &
  check_default &
  check_generate &
  check_native_modules &
  check_moment &
  wait
}

if [[ $# -gt 0 ]]; then
  if [[ "$1" = "-h" ]]; then
    echo "Usage: $0          run all lint rules"
    echo "Usage: $0 -h       show this help message"
    echo "Usage: $0 <rule>   run specific rule"
    echo ''
    echo 'Available rules:'
    declare | grep -o -E 'check_[^ ]+' | sed 's|check_|    |g'
    exit 0
  fi
  "check_$1"
else
  check_all
fi

if [ -f "$XDG_DATA_HOME/lint.error" ]; then
  echo "Found error(s) please fix."
  rm "$XDG_DATA_HOME/lint.error"
  exit 1
fi
