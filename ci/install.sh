#!/usr/bin/env bash

npx npm-cli-login \
  -u "$NPM_PUBLISHER_USR" \
  -p "$NPM_PUBLISHER_PSW" \
  -e <EMAIL> \
  -r "https://nexus.devops.moego.pet/repository/npm-local" # Please note here cannot add end slash`
echo "pnpm version: $(corepack pnpm --version)"
corepack pnpm config set registry 'https://registry.npmjs.org/'
corepack pnpm config set @moego:registry 'https://nexus.devops.moego.pet/repository/npm-local/'
corepack pnpm config set auto-install-peers true
corepack pnpm install --frozen-lockfile
