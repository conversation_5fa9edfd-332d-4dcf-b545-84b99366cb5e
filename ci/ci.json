{"$schema": "https://moego.s3.us-west-2.amazonaws.com/ops/github-actions/ci-json-schema.json", "service_name": "moego-business-mobile", "language": {"type": "node", "version": "18"}, "install": {"commands": ["pnpm i"], "cache_dir": "node_modules"}, "lint": {"commands": ["bash ci/lint.sh"]}, "build": {"commands": ["bash ci/build.sh"], "cache_dir": ".metro-cache", "upload_cdn": [{"from": "dist/toCDN/assets", "to": "/expo-updates/moego-business/", "exclude": "*.hbc", "max_age": 31536000}, {"from": "dist/toCDN/assets", "to": "/expo-updates/moego-business/", "exclude": "*", "include": "*.hbc", "content_encoding": "gzip", "content_type": "application/javascript", "max_age": 31536000}, {"from": "dist/toCDN/manifest", "to": "/expo-updates/moego-business/", "max_age": 1}, {"from": "dist/toCDN/runtime-version-info", "to": "/expo-updates/moego-business/", "max_age": 1}]}, "deploy": {"type": "app"}}