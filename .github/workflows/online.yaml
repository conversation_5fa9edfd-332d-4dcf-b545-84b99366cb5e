name: Release to PRODUC<PERSON><PERSON>

on:
  push:
    branches: ['online']
  workflow_dispatch:
    inputs:
      environment:
        description: 'Specify the environment to deploy'
        required: true
        type: choice
        options: [testing, staging, production]
        default: production
      skip_canary:
        description: 'Skip canary deployment'
        type: boolean
        required: false
      dry_run:
        description: 'Dry run the deployment'
        type: boolean
        required: false
      enable_cd:
        description: 'Enable CD'
        type: boolean
        required: false
        default: true
      experimental_mat_tag:
        description: 'Experimental version of moego-actions-tool CLI'
        type: string
        required: false

jobs:
  run:
    uses: MoeGolibrary/moego-actions-tool/.github/workflows/preset-online-app.yml@production
    with:
      environment: ${{ inputs.environment }}
      skip_canary: ${{ inputs.skip_canary }}
      dry_run: ${{ inputs.dry_run }}
      experimental_mat_tag: ${{ inputs.experimental_mat_tag }}
      enable_cd: ${{ github.event_name == 'push' || inputs.enable_cd }}
    secrets:
      ADMIN_TOKEN_GITHUB: ${{ secrets.ADMIN_TOKEN_GITHUB }}
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      DD_API_KEY: ${{ secrets.DD_API_KEY }}
      DD_APP_KEY: ${{ secrets.DD_APP_KEY }}
      JIRA_PAT: ${{ secrets.JIRA_PAT }}
      NPM_PUBLISHER_PSW: ${{ secrets.NPM_PUBLISHER_PSW }}
      SENTRY_SAAS_AUTH_TOKEN: ${{ secrets.SENTRY_SAAS_AUTH_TOKEN }}
      SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}
      ARGOCD_CI_USERNAME: ${{ secrets.ARGOCD_CI_USERNAME }}
      ARGOCD_CI_PASSWORD: ${{ secrets.ARGOCD_CI_PASSWORD }}
