name: Native Build and Deploy

on:
  workflow_dispatch:
    inputs:
      platform:
        description: "Target platform (all/ios/android)"
        required: true
        type: choice
        options:
          - all
          - ios
          - android

jobs:
  run:
    uses: MoeGolibrary/moego-actions-tool/.github/workflows/app-build.yml@production
    with:
      platform: ${{ github.event.inputs.platform }}
    secrets:
      NPM_PUBLISHER_PSW: ${{ secrets.NPM_PUBLISHER_PSW }}
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      APP_CI_CONFIG_STORE_TOKEN: ${{ secrets.APP_CI_CONFIG_STORE_TOKEN }}
