node_modules/
.expo/
.expo-shared
.cursor/
dist/
dist-build/
web-build/
npm-debug.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision
*.orig.*
web-build/
web-report/
.eslintcache

# macOS
.DS_Store

# JetBrains products
.idea/

# misc
.env.local
.env.development.local
.env.test.local
.env.production.local

*.log

.metro-cache/

dist/


# Temporary files created by Metro to check the health of the file watcher
.metro-health-check*

.env*.local
# ios/
# android/
*.ipa
*.apk
*.aab

# @generated expo-cli sync-ba457486f18e0afcb8f6494641422f0a1150e3a9
# The following patterns were generated by expo-cli

# OSX
#

# Xcode
#
build/
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata
*.xccheckout
*.moved-aside
DerivedData
*.hmap
*.xcuserstate
project.xcworkspace

# Android/IntelliJ
#
.idea
.gradle
local.properties
*.iml
*.hprof

# node.js
#
node_modules/
npm-debug.log
yarn-error.log

# BUCK
buck-out/
\.buckd/
*.keystore
!debug.keystore

# fastlane
#
# It is recommended to not store the screenshots in the git repo. Instead, use fastlane to re-generate the
# screenshots whenever they are needed.
# For more information about the recommended setup visit:
# https://docs.fastlane.tools/best-practices/source-control/

*/fastlane/report.xml
*/fastlane/Preview.html
*/fastlane/screenshots

# Bundle artifacts
*.jsbundle

# CocoaPods
/ios/Pods/

# Expo
.expo/*
web-build/

# @end expo-cli
android/app/build
android/app/release
android/.gradle
android/build
android/.project
android/.settings

adhoc/upload
temp/

# test
/coverage
scripts/openApi/*-debug.json
scripts/openApi/*-debug.ts

assets/icons/preview.html
assets/images/preview.html
scripts/ad-hoc/upload

# Expo
.expo
dist/
web-build/
*.env.local
moego-traceroute/android/.cxx
react-native-traceroute/android/.settings
react-native-traceroute/android/.cxx
react-native-logger/android/.settings