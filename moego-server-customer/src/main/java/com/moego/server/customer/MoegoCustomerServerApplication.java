package com.moego.server.customer;

import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.customer.account.config.CookieConfig;
import java.util.TimeZone;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;

@Slf4j
@EnableFeignClients({
    "com.moego.server.business.client",
    "com.moego.server.message.client",
    "com.moego.server.retail.client",
    "com.moego.server.grooming.client",
    "com.moego.server.payment.client",
    "com.moego.server.customer.service.tmp.clients",
})
@SpringBootApplication
@MapperScan("com.moego.server.customer.mapper")
@ConfigurationPropertiesScan
public class MoegoCustomerServerApplication {

    public static void main(String[] args) {
        TimeZone.setDefault(TimeZone.getTimeZone("UTC"));
        var app = SpringApplication.run(MoegoCustomerServerApplication.class, args);
        log.info("cookie config: {}", JsonUtil.toJson(app.getBean(CookieConfig.class)));
    }

    @Bean
    public AmazonS3 s3Client(
            @Value("${s3.key}") String accessKey,
            @Value("${s3.secret}") String accessSecret,
            @Value("${s3.region}") String clientRegion) {
        return AmazonS3ClientBuilder.standard()
                .withCredentials(new AWSStaticCredentialsProvider(new BasicAWSCredentials(accessKey, accessSecret)))
                .withRegion(clientRegion)
                .build();
    }
}
