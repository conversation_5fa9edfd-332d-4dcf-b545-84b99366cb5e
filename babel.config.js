module.exports = function (api) {
  const env = api.env();
  api.cache(true);
  return {
    presets: ['babel-preset-expo'],
    plugins: [
      '@babel/plugin-transform-logical-assignment-operators',
      env === 'development' || env === 'test' ? void 0 : 'transform-remove-console',
      'react-native-reanimated/plugin',
      env === 'development' ? 'babel-plugin-react-anonymous-display-name' : void 0,
      'nativewind/babel',
      env === 'development'
        ? [
          require.resolve('react-native-components-inspector/babel/injectInspectorSource'),
          { propName: '__inspectorSource' },
        ]
        : void 0,
    ].filter(Boolean),
  };
};
