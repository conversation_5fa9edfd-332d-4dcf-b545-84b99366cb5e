<!doctype html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1,shrink-to-fit=no" />
    <title>Download MoeGo</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        outline: 0;
      }

      html,
      body,
      #root {
        height: 100%;
      }

      #root {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
      }

      .tips {
        margin-top: 12px;
        color: gray;
        font-size: 14px;
      }

      .btn-download {
        text-decoration: none;
        color: white;
        background-color: orange;
        font-size: 20px;
        line-height: 48px;
        padding: 0 24px;
        border-radius: 24px;
        box-shadow: 2px 8px 6px 0 rgba(41, 44, 70, 0.08);
        font-family: -apple-system-font, BlinkMacSystemFont, 'Helvetica Neue', 'PingFang SC', 'Hiragino Sans GB',
          'Microsoft YaHei UI', 'Microsoft YaHei', Arial, sans-serif;
      }
    </style>
    <script>
      function handleDownload() {
        const directory = location.href.replace(/\/[^\/]+$/, '');
        if (/android/i.test(navigator.userAgent)) {
          open(`${directory}/moego-business-mobile.apk`);
        } else {
          open(`itms-services://?action=download-manifest&url=${directory}/moego-business-mobile.plist`);
        }
      }
    </script>
  </head>
  <body>
    <div id="root">
      <a class="btn-download" href="javascript:handleDownload()">Download</a>
      <p class="tips">Branch: __BRANCH__</p>
      <p class="tips">Last update: __TIME__</p>
    </div>
  </body>
</html>
