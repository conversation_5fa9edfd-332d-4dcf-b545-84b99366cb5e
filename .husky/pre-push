#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

npx git-branch-is -r "^(feature|bugfix|gate|testflight)-([0-9a-z.-]+)|master$|online$|staging$"
git fetch origin production:production
shouldCoveragedFiles=$(git diff origin/production...HEAD --name-only --diff-filter=ACM | grep -E 'src\/(libs|utils)\/.*\.[j|t]sx?$' || true)
if [ "$shouldCoveragedFiles" != "" ]; then
  jestConfigPath='./jest.coverage.js'
else
  jestConfigPath='./jest.config.js'
fi
npx jest --coverage --config=$jestConfigPath --changedSince=origin/production

command -v git-lfs >/dev/null 2>&1 || {
  echo >&2 "\nThis repository is configured for Git LFS but 'git-lfs' was not found on your path. If you no longer wish to use Git LFS, remove this hook by deleting '.git/hooks/pre-push'.\n"
  exit 2
}
git lfs pre-push "$@"
