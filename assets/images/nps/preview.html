<!doctype html>
<html>
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="initial-width=device-width" />
    <title>MoeGo Icons</title>
    <style>
      .content {
        max-width: 1050px;
        margin: 0 auto;
        text-align: center;
      }

      .icons.orange {
        color: orange;
      }

      .icons.dark {
        background: #e0e0e0;
      }

      .icon {
        display: inline-block;
        text-align: center;
        margin: 25px;
        width: 100px;
        word-break: break-all;
        vertical-align: top;
      }

      .icon img {
        display: block;
        width: 50px;
        height: 50px;
        margin: 0 auto 12px;
      }
    </style>
  </head>
  <body>
    <div class="content">
      <h1>MoeGo Icons</h1>
      <label><input type="checkbox" onchange="setOrange(event)" /> Orange</label>
      <label><input type="checkbox" onchange="setDark(event)" /> Dark</label>
      <div class="icons">
        <div class="icon"><img src="./app-BG.png" alt="app-BG.png" />IconNPSAppBGPng</div>
        <div class="icon"><img src="./certificate-blue.png" alt="certificate-blue.png" />IconNPSCertificateBluePng</div>
        <div class="icon">
          <img src="./certificate-green.png" alt="certificate-green.png" />IconNPSCertificateGreenPng
        </div>
        <div class="icon">
          <img src="./certificate-orange.png" alt="certificate-orange.png" />IconNPSCertificateOrangePng
        </div>
        <div class="icon">
          <img src="./certificate-purple.png" alt="certificate-purple.png" />IconNPSCertificatePurplePng
        </div>
        <div class="icon">
          <div class="svg">
            <svg xmlns="http://www.w3.org/2000/svg" width="65" height="65" fill="none">
              <circle cx="32.4" cy="32.4" r="31.4" fill="#fff" stroke="#F96B18" stroke-width="2" />
              <path
                stroke="#F96B18"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2.375"
                d="M32.902 36.509V18.775M44.302 36.509a5.067 5.067 0 0 1-5.066 5.066H26.569a5.067 5.067 0 0 1-5.067-5.066"
              />
              <path
                stroke="#F96B18"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2.375"
                d="M39.236 30.175 32.9 36.51l-6.333-6.335"
              />
            </svg>
          </div>
          <div>IconNPSDownloadRoundSvg</div>
        </div>
        <div class="icon">
          <div class="svg">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="25" fill="none">
              <path
                stroke="#F96B18"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="1.5"
                d="M12 17.333v-14M21 17.333a4 4 0 0 1-4 4H7a4 4 0 0 1-4-4"
              />
              <path
                stroke="#F96B18"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="1.5"
                d="m17 12.333-5.001 5.001-5-5"
              />
            </svg>
          </div>
          <div>IconNPSDownloadSimpleSvg</div>
        </div>
        <div class="icon">
          <div class="svg">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none">
              <path
                stroke="#F96B18"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="1.5"
                d="M4.5 9H3M4.5 15H3M4 12H2M5.305 18a9 9 0 1 0 0-12"
              />
              <path
                stroke="#F96B18"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="1.5"
                d="M11.103 8.086a1 1 0 0 1 1.794 0l.508 1.031a1 1 0 0 0 .753.547l1.139.166a1 1 0 0 1 .554 1.705l-.824.804a1 1 0 0 0-.287.884l.194 1.134a1 1 0 0 1-1.45 1.054l-1.019-.535a1 1 0 0 0-.93 0l-1.018.535a1 1 0 0 1-1.451-1.054l.194-1.133a1 1 0 0 0-.287-.885l-.824-.804a1 1 0 0 1 .554-1.705l1.138-.166a1 1 0 0 0 .753-.547z"
                clip-rule="evenodd"
              />
            </svg>
          </div>
          <div>IconNPSDrawerIconActiveSvg</div>
        </div>
        <div class="icon">
          <div class="svg">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none">
              <path
                stroke="#FFF"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="1.5"
                d="M4.5 9H3M4.5 15H3M4 12H2M5.305 18a9 9 0 1 0 0-12"
              />
              <path
                stroke="#FFF"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="1.5"
                d="M11.103 8.086a1 1 0 0 1 1.794 0l.508 1.031a1 1 0 0 0 .753.547l1.139.166a1 1 0 0 1 .554 1.705l-.824.804a1 1 0 0 0-.287.884l.194 1.134a1 1 0 0 1-1.45 1.054l-1.019-.535a1 1 0 0 0-.93 0l-1.018.535a1 1 0 0 1-1.451-1.054l.194-1.133a1 1 0 0 0-.287-.885l-.824-.804a1 1 0 0 1 .554-1.705l1.138-.166a1 1 0 0 0 .753-.547z"
                clip-rule="evenodd"
              />
            </svg>
          </div>
          <div>IconNPSDrawerIconSvg</div>
        </div>
        <div class="icon">
          <img src="./smiling-face-with-halo.gif" alt="smiling-face-with-halo.gif" />IconNPSSmilingFaceWithHaloGif
        </div>
        <div class="icon">
          <img src="./smiling-face-with-hearts.gif" alt="smiling-face-with-hearts.gif" />IconNPSSmilingFaceWithHeartsGif
        </div>
        <div class="icon">
          <img
            src="./smiling-face-with-sunglasses.gif"
            alt="smiling-face-with-sunglasses.gif"
          />IconNPSSmilingFaceWithSunglassesGif
        </div>
        <div class="icon"><img src="./star-struck.gif" alt="star-struck.gif" />IconNPSStarStruckGif</div>
      </div>
    </div>
    <script>
      function setDark(e) {
        document.querySelector('.icons').classList.toggle('dark', e.target.checked);
      }
      function setOrange(e) {
        document.querySelector('.icons').classList.toggle('orange', e.target.checked);
      }
    </script>
  </body>
</html>
